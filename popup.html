<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>东莹数据处理工具</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <div class="title-header">
      <img src="logo.png" alt="东莹Logo" id="app-logo">
      <h2>东莹数据处理工具</h2>
    </div>
    
    <!-- 文件上传区域 -->
    <div class="upload-section">
      <input type="file" id="file-input" accept=".xlsx, .xls"/>
      <label for="file-input" class="upload-btn">选择Excel文件</label>
      <div class="file-name" id="file-name">未选择文件</div>
    </div>
    
    <!-- 网页交互按钮区域 -->
    <div class="actions-section">
      <button id="initialize-shipping-order" class="action-btn">初始化送货顺序</button>
      <button id="update-by-json" class="action-btn">更新数据</button>
      <div id="action-status"></div>
    </div>
    
    <!-- 数据展示区域 -->
    <div class="results-section">
      <div id="status-message"></div>
      <div id="table-container">
        <!-- 表格会动态插入到这里 -->
      </div>
    </div>
  </div>
  
  <!-- 先引入第三方库 -->
  <script src="lib/xlsx.full.min.js"></script>
  <!-- 再引入自己的脚本 -->
  <script src="popup.obfuscated.js"></script>
</body>
</html> 