body {
  font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  margin: 0;
  padding: 20px;
  width: 600px;
}

.container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-section {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

input[type="file"] {
  display: none;
}

.upload-btn {
  background-color: #4285f4;
  color: white;
  padding: 10px 15px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.upload-btn:hover {
  background-color: #3367d6;
}

.file-name {
  color: #555;
  font-size: 14px;
}

/* 新增的按钮区域样式 */
.actions-section {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.action-btn {
  background-color: #34a853;
  color: white;
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.action-btn:hover {
  background-color: #2a8745;
}

#click-first-row {
  background-color: #fbbc05;
}

#click-first-row:hover {
  background-color: #e8ab00;
}

#action-status {
  width: 100%;
  margin-top: 10px;
  color: #555;
  font-size: 14px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 10px;
  font-size: 14px;
}

th {
  background-color: #f2f2f2;
  padding: 10px;
  text-align: left;
  border: 1px solid #ddd;
}

td {
  padding: 10px;
  border: 1px solid #ddd;
  word-break: break-word;
}

tr:nth-child(even) {
  background-color: #f9f9f9;
}

.export-btn {
  background-color: #34a853;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 15px;
  transition: background-color 0.3s;
}

.export-btn:hover {
  background-color: #2a8745;
}

/* Header with Logo */
.title-header {
  display: flex;
  align-items: center; /* Vertically align items in the center */
  margin-bottom: 15px; /* Space below the header block */
}

#app-logo {
  width: 32px;   /* Desired width of the logo */
  height: 32px;  /* Desired height of the logo */
  margin-right: 8px; /* Space between the logo and the title text */
}

.title-header h2 {
  margin: 0; /* Remove default h2 margins for better control with flexbox */
  /* Retain or adjust original h2 font size if needed, e.g., font-size: 1.5em; */
  /* The existing h2 styles for color etc. should still apply unless overridden here */
} 